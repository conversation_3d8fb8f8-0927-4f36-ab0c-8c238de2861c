// Package api provides primitives to interact with the openapi HTTP API.
//
// Code generated by github.com/oapi-codegen/oapi-codegen/v2 version v2.4.1 DO NOT EDIT.
package api

import (
	"bytes"
	"compress/gzip"
	"encoding/base64"
	"fmt"
	"net/url"
	"path"
	"strings"

	"github.com/getkin/kin-openapi/openapi3"
)

// Base64 encoded, gzipped, json marshaled Swagger object
var swaggerSpec = []string{

	"H4sIAAAAAAAC/+x9W28cyXXwXynMt8BHITMiKcnGmi8JRUmWvJKWICVvNjJNFKfPzNSqb1tVTWosE8gQ",
	"huHEMYzEyEMQGEGAIAhyBQIEeQiQ/JiB/0dQt+7q7uru6uHMSOv4jZyu6zmnTp17vR+MkyhNYog5Gxy8",
	"H1BgaRIzkP88xMEJfJ0B4+K/cRJziOWfOE1DMsacJPHuVyyJxW9sPIMIi78+oTAZHAz+324x9K76ynYf",
	"U5rQEz3J4Pr6ejgIgI0pScVggwMxJzKTXg8HR0k8Ccl4iwvIZ7weDp4k9IIEAcTbm76Y8no4eBZzoDEO",
	"T4FeApV9t7cSMzlSsyM1/fVw8DLhT5IsDra3lJcJR2rK6+HgdYwzPkso+RFscQmlWcVn3VMMfDjm5BJz",
	"OKZkTOKpoCAytU5OSpMUKCfqVMFkAqIDPKFJJH6YJDTCfHAwCDCHEScRDIYDPk9hcDBgnJJ4Kradd3sd",
	"cxL69rseDih8nREqYPWmMvdZ3jy5+AoUyR8GEYmPMX1L4unzhD8Cjkko143D8PPJ4OBNOyCLroPrYXXr",
	"+BIonsIpMEaS+FFGsYLue7MOEnOYgqSysYSibiJ7Ew4R60JkMb9CgxhKD44pxXPxfwr47dMko+VRaxCv",
	"duMJx+EJXEKcgXvNsoXeHXM1ua5B3AMHp1kUYTqvUxIOAgrMvfgxBcwhOOT+FDbOKIWYfz4eZymOx3P3",
	"HklQGjHLSOAaLMYROBfGOOZZJxrFtlVDCdYAzz0AnybcE+o1GLP87NegHJKIcPe0oZ7QizIbsOogtGQy",
	"YcBbdtpzk/MI4lVOsuznOMYBYWnGwX/rj1QHswTHlilMsjjwH/FEtm8ZkKlT6IUUfWItjGQMqFfX1wxo",
	"3q/X6ZbAbT7aUZLFDSSwwtH2PLRpTp0vm45vqhb+AvgsCdwtQszhZRZdKBDWGYCC9jO/JfmxCwPOnGWI",
	"AVLQPz+TAsIz93IFqj3XIpo2AKbr9K3GX9oYgUZEb/5TorumW66F9xw2UmYzDARF9Oc/opeD+4SY8dcM",
	"An/qpzAWe7buZS+A6Q6NQDq2MLCCLLANLiNA2MhjNsdHCJMisX1QLpIkBCyB2R+BXSxFfn8lf/WgKdnQ",
	"H0OPI0zCD8I6xGI3wDjksD3Zhk1IfZhG4+aMDtCXKxSHsgqOCcBDCvhtkFx1XvtP7Lali89fv7CYsOf1",
	"lEPfi0y3wiEqos+m5ZBA63MvSGwEyPq4EHM6f0XUYfFUjt8R3q+HNyc7xiRw8zEfaalLFvKSbgySculm",
	"w4zH8MS1sx7W9wpuEM9vz35ep4I69MiNppo26g8IG4uvhy1t+pPlSgTRsUvBG5q3mPFEs6fHMb4IoYHY",
	"ofEWlF++D5RMSFPnRmNAOkti9xeahJ1sUm5MtPMGnGSTpWMU9zwbYgTr0loNmCtwzlvB35PZCcHseTIl",
	"cZ+FrYDbnhJIfhvWjAZboBGlSB5ljCeRYbp1S+Fa1AFtxIImbpLJ09yLalal8HXqDN88yv2Yec/6L+UW",
	"hV8ssudlXRJEq0fWubOMz5r3hMdjYOxV8hZiNwm9SwkF9ixuNZKcAs/Sk9wP8r7i2/liBnwGFOnWiInm",
	"iDCUu06GDtKkMKHAZs1rk8M8y51T7Rd63rRwQvmJ/wLgjZA1t5GHhFPxguJwnAm+GyAt5qIJACIx+t7x",
	"lwU4bG0g7/EEoD7gEwBkmpAkRhe5+jWsLOkCM9BD1GeZkBiHDV9dMJhCDBRzOIExkFRvdIKzkA8OOM2g",
	"6ub7rm6PcBwgBnGAqOqJxN0eYU7GOAznToIQzV8mnEy0C7JzslMxviE6gdoQJGxiexDXTBmDR2rg46o5",
	"tnXC1wyQoKn/z5Bul08fqRHqs1U8iJpgqvg+66K/xgPepmBiGjykOHZfuuLrc8z4A/dX6cxSTqwcJoMS",
	"8VpaXnyZkHGbohYBY3gq1w/vsMDU4GCgN4dSmgguBQFimeRXk6xEI7b1nPS6OzV2nvma7y1iMOYwpzWU",
	"pPw1DW+hkt7C4K5h5LrvXSf4YZII9uMQRXA8hlCxk0Z+oRv1dISuIOTEQT9Fb5ZkNJyfaGPQym7W1Q1X",
	"5j9f2uplt5Kt/T08dCNasqYc24/c6r9YWcL22mgLbZ/m24E4iwSXlcEPNJKSR07C4m91Sci/4+SczZIr",
	"i/UWqzqa4XgKx5ixq4QGjbe/9vibdg4xQDVAqWnhEqPhqnmAl3CVd0Y7EYlJlEXoUzSeYYrHHCi7MxgO",
	"IhI/h3jKZ4ODT7tCWKprLi/AdQ8dyeOsYd0cltP3BK92hDZ1KCpQMnNVlmkPXDCtZqAVTKNZgmwLQgkT",
	"BmYX9a9JzPGYHzfaCQJMwvkL/K6ZS04A84xCz0CeCQVotUDPgExn/LnQp46ihiZd/DvC08q6CqRT4sJ5",
	"dZmE3Xswa3KhccKzoEIdSXYRWqQRK3FGxqnE0z7tGzXjJIW4EZ+CKjBPaKM1vDNKx6ZguYZhTl7Wnu39",
	"lAZtoeNQ6gBNbDDEjJXE9gi/MwzpvuvyJhRPcYwrTfeH63JNUpjWV7K/52IUQAkOC8nVav+gi0noWYZV",
	"AFgbrEzQAmGv2EcV1GcIpLQ31+ZSNehJFkK3RGq3rV0axbyVUV07culWzRqMUzV45FSvkBYwPPQEa+r1",
	"yNGPgMNYoPcRoeoPW+CQPreB8lg4JYq8e5utxjnquRan9ejnFMYJDeT/2h90rsGihBo9jGsRHTaGVqMF",
	"2pkkFIkVIDWDlDzqjNtCZ9OVbweullfwSjCjfHLj7RQriNR947sKT8eyS8Wuux3dJo4ACfJDsi3SvH94",
	"63CtnkRZDgzctPsZLkkA8dhGbrEWT42LAmZJ3KE6Oz4J9Vhv13mgXeB5XBjIFWtuZKzcWCHLuJYDoEtr",
	"BCRboglNIhThKRmjkMRvO+VJNb6LWZYj5x0cP2gQ7gqc18YEk+nQZoepixckAsZxlPaQmWtTP6lEkfSw",
	"TUpx9Qinh2kalh0aPd3GbUbO4SAWsmnXNMklUEoC6GEqfQJwVBhojylcErhqJLjVr+bhgI0hxpQkvaP6",
	"T3VHp2ehpP7YM9oTnvlvvImi+6/eLPsEWBZyP7fIk4ROE96pwefOtZze1S+diSCy1ZnHxL3EHtMNUWDA",
	"EYkZp5m65xATchBP0DzJKGpeZW1BTzENrjAFSwJpAMb953UOeH8UkCnhqCziFvddijkHKlr+8M3e6Dtn",
	"7+9ff+Ji+0f7DnmD8Dm6xGEGaOd7OMUxMEBKqEYheQvoN79a/OY/fy0tHB1i/FPH8PsoBLE49FRL4yih",
	"CNNoPqKgpBz9fefx8Onws+GL4avhl9XJyjtcLv5yufir5eKvl4tfLxd/s1z87XLxd8vF3y8X/7Bc/ONy",
	"8U/LxT8vF/+yXPzrcvFvy8W/Lxf/sVz813Lx38vF/yxv/nh5s1je3CxvfrK8+eny5k+WN3+6vPn58uYX",
	"y8XNcvGT5eKny8XPloufLxe/WC5+uVz8+XLxq+XNXyxvfrm8+bPlzc8eP/3sxasvz5zwffaoDoAHGndK",
	"BWnB2QM3zqTUn1/45bFzakJFK8TGCRX3RF1FjvA7Egmhdl9aq9Tfew7VWer92rZenvFz+QcOkWyCXp88",
	"V7dwYFbSYmQiDtPatzV0jLQZJhyJfXAyIW4wfdsNpkJsKU+wd4CktQgCcWzFNGhHSvR3hmj/AIUw4YjP",
	"oLSAHSHa3pG2JakC7A33z1zyLXfM98rc3yiZlKCygv2LC7Z/tD8YCqYgj5ckMRug+cZdXFAGEzQyGgvM",
	"tV281o59cVgll0OFCcOB3ibDqRimxexa2W4F7y3G0CKxytLTsIrWFmQucBRjcWS0+S5wamIvFPf3uh7K",
	"GzNdkGnRrkVU/Jk+SsVLLSC5XEbyR8cBLoWfBuVLbe/Tg7298nnaebM32j/7fXmsfnzvzd7o/tmdgzd7",
	"o2+dyZ8+aTTplke+d28NI1dIQU2jtjGUO3aRQdVPvb3MgNNyfJNl1PTQvHsY23t5ToVs0me3/ZRiTngI",
	"LZbpdunRRpWxD27bUVVbg8U8BGpAeRokyM9tJ5XQQxjH8/MrTGMxvDK/wTkOQZLphXLLnFOISByUTIzF",
	"2o9LXs4V/Q+3PL6d3orVM24PLzEJ8QUJCZ/7u3FLvRzekjLbfaEEGCRbIRni0hLMExDGsVtu0l+kTQs4",
	"UG3SYoDpeCbk/izk0qjl4WdYi/umEmdEoZBItNHNucXePp7KNdYBQV9+WHcTfdP9Qrc8Z51epJUSyNsc",
	"TyuwUxePbDiZdYalvobQsiCZIqeN4t7cJDGJ+4ZiPTDaP3G+VmChw8mzFj6pujyce52prRbYWFNOZH/5",
	"5hb2tzWT+xZqJ5SDlzZWL6FqZnQEiWwhNa05LNzhHy9WZE115txb7lHyCwFWFur2kN9bxGeuKi15hnRO",
	"MAkzCifN3hrfc9sZG7piUOftwjQpcDo/avYheNa/sHLcP7K6DKWWz0n8trXdabPrbdu6kkkAa/eLNbu4",
	"5Jcj7TYzShaJWTaZkDERCpYqlaJO2HkA45DE2r2eCu5wLn5XvnSjkuXR3TpUWWhcagVnzrMThhd4/FZZ",
	"LB1C9qERYZBpihLVVtm9Ixxn0g+ugh+0h9Ffwm2zBEjCF3O7L1KX6WiCQ+ZpO7LobbWYdZ2QskqQd9tp",
	"SIt1uTlCy2aK5ICKM9Ow7moUBQ2Q/IZ2vk8YHqIXmHGggqqGCPj47p11BU0LWM1fJDGfNSxDtlAem0g0",
	"Qzv7o/177pAJNdqXgGn3YHPRqlltcwhvjwwxNSUP8RlhiDBpEfdOrlCS/oP6qOLiRA+QtPAzlEyQgH5b",
	"lIbNLVvDhozJqTzfcTlOSTYaFuHIiqGkeJ7iuZNlfCAmK/Omvo9DEuCO2CRnZq4bk6LtyKCOMASqA5ok",
	"VGJXZmW5cDnDrDktx00zajQ0wwzhBrJBDEIY84YstBlmK00WzyvTMISDoGESwiSgj7Rhz3tDlwIz1e0I",
	"MNogZs4pY3jHTzmkrpDydxxRGCdRBHEAAWIcUsQTk0EFKn/PIl8cBPWbkAE/1wCvf1QoPxfLNB8HhsjP",
	"a9dsDM4zoaV1qfe5gsVqQIsBAiZ2wvFb0PFh4l+1mhLQBr2us7rHJ4U4MOZYIxFU8guEBC3/ULXZGhxB",
	"xyYvpCuktsKQXe54tLN/737FgX1/PVeNHbFbXknuXM9TEtDOcnFT96NvSPFvC9V7koUhmubxel2hereN",
	"NS5PXo1o2PGPZqgGJVfTHi13vsT5g8qgDz6GG8YGpjk2sRgnVAclAjom8h8KMZd/RCQkHFP3FXlMyRg+",
	"13FZzvyTdRRdkM6VFOh5FivLS7uzM3dKekI0Jvw8Kmzv+eD7Q894/rJ7sjRebfVOM0Zhy6rDMMDzsk3J",
	"4O2FjCN4lYkVfCGZ2qtZNhgOnshMjFMsVnSauYOOq8qJRlQPM/ePf3jvwcHe3idNdryVEHZLM/stMCmh",
	"vFZMMhcqSTg/93LYk5hwgsPzCQVwbsnZK0z4eYO0LyMsxdz+Bso85sBlntRnvoe5s8QqXJVPDNC8hzNn",
	"pityUoOlAapDCy9mFS7cnli1ERqjaAK4JGMdDN0QKqWaWGFNSPkapaUKcYrHb5Xg4hC+Wqsz1DJhrNYN",
	"G7LqzX4s5VpN3rmnY+Q2oesKAN6R66r5imn/G6xv2ycoxAtcdYFaWqAgsGXos+HawDoljLeU8/KOBy5k",
	"CIfIqe9qSyK7587PagxfywOBo4xxdAEIcxQCZryU+9uV+msVkaoML34uJOHSQp0HBSZAKQTPcTzN8rA0",
	"40L4Cg+qlTKOTQ8U6i5onASAdr7CkgUZ4XiIIJY/PI6nIWEzK/RRDQtuoUKqqP2Kd+TqYqIL1FT06iAT",
	"g0tpnXHaWjyEunH/OiZfZ0oXlRGMYl9hMiVxGcTf2iuh7X450hSPfnQ4+qO90XfOR2e/1x2ulq9mmFOq",
	"FfUYm4y9Kv7cPJpBHHily+THxJUuoyOKBKCpHLKaQGMHt9w23F4sujvMP/VKsO8+Tg1pQnIROjPIhOnr",
	"OjF++1PjdoSeVrZ6m8SCIn/SzbgDmeOoLeXFKLuGmj1YbSVpo36DqfSbk5ok1qlAXJBQOk9aU9AvVi1l",
	"O2ksnVSETZnqy47kotbc7ZYVO0FY+CDX5nJeoeasiSQ/KqUDeATH+ORq+Ve0fWZlBjjqx/QPLKA0oS9a",
	"PGXwjvD2ST9E1VzlG7TtIG3RMJuuarPh+s09S36sVAt4y3axvGZuY8anfURrIkwszd1Gg7vCDBXp4b7n",
	"uUltfFTVFpu7v3QKQE+zCMcjCjiQBnetfWohpGGkV05/ml6K6IN2ouSChDBEAbC3PEmHiIvh+Z3bnLD0",
	"sIh7rrzbdZxLMMnEhrb7pOoSP36OTR2wXB/RUd7fRQDSrSkQq0a1ySBj3jTQQpYtuS22b8OuqdQcB9Fd",
	"B5ut+KaEnazfnQvqqg7p8LgQ6RAdU3AA/lRqm6aypRwKqR6IqS7OgCu76q1zQNMEyeSq+g2TziACisPP",
	"YO4Qt81X9BbmUttQJwWdPvrMnS6VXQglS5we54B6UVYzMbKHiFjRg06ey9WYRmgSJleITKSDDhoSSHIU",
	"tUCrBH4XxJzYVwFO0gGro22aE9PKcQrl+JeqF4YjzPqEKjiKkrjBX9ZL3ZSxPoCVkp9Ko9aX7FJLVJz1",
	"7ypd/a7S1foqXa09R+G6mXA/usJLDQtd4RmCynUhP+wGhOXhELIgb85wSqEMdVIKHLE5fdhYHrlSmpW1",
	"1jxvsy/tXJEwNDWmEYVRycxEJmgsqzgGdzzsTWsx58q6w/PHDWsvQ1+lNtuVihvieOSYxxmbdQ6ZZmzm",
	"MeJGTcLbtAF/eENs/aTqguN+h7PFWP047jyjKInzRD1Zdmy4npcyPsQx7z6eH8lbB83coYsbdB3tVQ/q",
	"Gk+m57n7aJ4NueWDHq08w/+8n2hwGNDp8FYcRMQNP2unLj2bZSyVgZlC/tZeUdcwPmG7UjfvKfPKoFM3",
	"Reok9F4D1sEmtZdxRgmfnwrk68fyAVOgh5mKZb+Q/z0xOP3eF68G+slwuST5tUDRjPPULnVkRiECsTPA",
	"gWys8D74w5FpNnql3S5mxSkRarF8vZzEk8S8jo7V+/maWw1YlqYJ5X/AoiThs7skHheDn8rf0LN4PKg9",
	"gX4aYVqUm4lwjKeg3suYMw4RuiJ8ZjHOkIwhZqCjFnMr/PAHcf0uGMr3FijgUFK+Y5K7PxAb1WOK3egF",
	"H6Z4PAN07+7eYDjIhDYvockOdnevrq7uYvn5bkKnu7ov233+7Ojxy9PHo3t39+7OeBRaJRrM/rU2iF4U",
	"mzw8fjYYDi6BMl2d6e7e3T2jKeCUDA4G9+/u3b2vynnMJE3s4ozPdpUYN7J9eGmihGClSOisq8FxwrhA",
	"fbl690CpuMD4wySYr+3Fe3eJ8OuyRi2udvmDOqByU/f29ta2iGoZGcfD+7nnT0vD5dqp18PBA7Ue1zT5",
	"uncf4mKLost+d5f6i//mUSRdXr0obK4lhhnEXMABgjxHAAtm80Y+cqM/ytqmYjhFHBNZbq0HcZTrs22I",
	"ONzV57ZMHA2V6NpoRHmHlW4iHdm3JhaL2Q8O3pzZVKDbFGQgZ/fCuhLmO3H9XMv8m0BxqcLVljFbevPJ",
	"gU+5NtvJv/qZbcCdmsH7lOr4wG6M6cDHDeHMFVb5kaFOrg1peDm59e1YrwYBUg+DIRN44oNCFTrng0Pd",
	"clNILMfweSFwf2sIlIXnDLDWdd1+p7vLURJPQjLuYrlqXQijGK76nF8GcVAysPnQQTWebGMU0RS49vGJ",
	"Y/Yqt3fRyig8qBezNrWrvQigj5RVilXbINo/tIzljsnrFrGqd/O2RPAWCrEEsUzaF/3vBtuNPwUHWXwX",
	"JFXk77duECO1oAMHMkwbRIFTApcbuGe/C9yEgCrLDmLF5v0hKiTdJOMjHIbdh85s67nscxiGgw/L6w7D",
	"ECUyBsZsB3GgEYnrb3isAeJq16omMA7DYk54N4aUm9ibfuB/n1d1uVYmQ5O/XcbAI/m7jYPTvBqMDKvD",
	"qtbf4OCNtkqlmM8KsxGzWpeZ19BCR1e02dmHRfepSStaK45FpwfdnV4m/EmSxW1EwVIYi6vPjr7qpAR5",
	"Xc5Huaeijb3Ju11b/GtYX/lhCUkuX2cg35fR9GJYczOtbJM2ukRie7Mf1ADVcPspvJWrTes7sMCE33Wo",
	"y7G23oQPTRs3Z6igWntPhp64qDzaeD10j5ql4yTS+Vb5uJ0Ff25LRF4hfubB0ronoUZYBpKN93jTlXxR",
	"oMCgM8fK2fWw5Z61kLcRq7LrrcUta7g5AhoBbmKOP5ByW9iR5TK0Mqtx6kapfTZ33+u/vC51M8hD08fr",
	"Qr+wWn+cF7oPlk2U73q00wJtclyE21E27GSg/wdRol99alVcVpeWBIe8KM/UfJxm2ou6Wzw10aqh1N6h",
	"2RALbXzvZsumgfqLfw68Fs+oNDyCfntT0PuKW/zN2XXZlO/2MytJ1CBZv1yya54nMTRhhtY0UY4+a5GB",
	"XlbC1DwEoSymgIN+AkuD9KPK6jpHuucodrEduaf0pISH8FMCYW8JqBYnqBFaRowDq7vv7X+fBde7EaZv",
	"ZdKPLqsynjkYgPi5NPbL0igvMH17otDbzcnLC/gmq8w2EFAkH4JAWCAT31rzFRAtYTkfuB3ZOnRkZOpL",
	"N51gq351l7L7XMeg6zhMNd/oAktmJ989aFBzQ1w+pZ0B6eLAV12hOqC9/+QV7WiFyU/1ow44IBkrXnxo",
	"mFA1c7Ol/b09F2Nq4G8soQ3srXiZooj1s36SZYcc0Wb1rT0hIQeKLuYoz6xw78r67GCIncFjKzHw4rWx",
	"PlDTZdCdo27iVvDLy2XtXoX86TDPK0CTpPXmmM39zXjyVNf5we77MOFaa/LgC+bN/G6OHuqWHycjt5Ok",
	"W3GwBfE8rc/WH3+7uPLChi8ySy9z/DYhtvwYkMOVYX1HJFaLFpeqt9xVBGfiMhBbcScDPUe6wmw7pqxw",
	"/Vs72DwflbDL73hIrMeVgrl9ZVaGL6FaDrdM/eqLAUK7JW+9EGt3Sdbzj53+Ezu5eF0WNpfBzJUdKmMw",
	"g0BQoLKn1TNaG8HsoNVdFcE/GuuU23bbQBkXankmWXdDZoKWtOAtGwoq56jr3Gi05en4ayaRpzgOwgqJ",
	"6Lnka68FVvrRw6VKV4CRqi7tzcp0moPC1GDzaGgqid6CFwWky7xPBaJmB4hMiuLetRrlHGWpu7Z3LzC/",
	"r6Rse1i4y/Cu5n353PJprc9tHJMPthseX8IDhShZt9h2IsdE2Hl/3R7Buwz4KCjKFvhy2QqiT4Gb2gfb",
	"R/k6ra717MV2A6zzzQCdWrZWOjiF2jRWAYnmrEkf8vCRDT3trH2Nox+JNt1HZO0jrM4I4wmd9xZW03L/",
	"OiKrGNy9gotZkrw1opPXWWZf6E7qvr6FrFRN0vvYAmf1Rhv9JC3xHVqW0fBFEAdpQkqRYE0oscK/dpVE",
	"bm7mkYzO8UJRHgqmpG/riaTfktgw16NPLSdKRtNsQ7+x388qFb7eKT+wdacnJWgSHJUeFOlHCsdqiMOi",
	"MsC2aWH9GpW1mQ+XXFOsoCUutamqwroiWu6t+2iVH8NrOVu6YHZPF7CmRke5CXFqLE2zFjhZOS4+geBW",
	"EPj6w98q5R3X5QD+Jsk41pOU3TKOMjmyrnj4ZiGnPIBFGTmay5Sxi/PHfLoI5NBURPioYKZWVdt5b9Bh",
	"9zgeEKzEpHeB8bctEj3HVSc9b9EdU5mxGYsC+2xXxtq0csnXot2xarYVzciU3O2i/1K4UH+yL1W1KKm3",
	"6od2m30VKpsKwJVTfKDwW42JDsir1wY/dADuYRBob0EJry60Vol/970u7uxhobTQfqwrQnvZqPK26+Rk",
	"WzVPlnG+UetkHwzSZEJC6OZfut0GbwNZca0pDVuvc4P8P7OmsWAmd694WeZiZVkdPutnZfWykVtWx5pw",
	"o7fdYmS9jQ1AbbsLMzUNya4/pfUjoJfugLZjmgSZipJVjWq1k3BK7hb1oXYv9weO6DCOp0p2cA7B1OeR",
	"z1CP4BLCJNXeqNpwB7u7YTLG4Sxh/ODTvU/3xPrkSGc5ZN67DhAu5TjJIlMmp8ok9Js6UuVsqPoS5XhW",
	"0av8RLBiEIUcRxBfiQsWgxQ9NZOqd7Wjc+y4DbGVPPzPDGJHYDQPZWQ91zpyaa/e3ajg0uE+1Q9pG1uk",
	"BYQ8pNq1gloRSLGPwsScb0Rr5c2boCDopIqAPMS/AX/VqGHdrRxHWu97GETE2qu0KtiBTLpcLlJVdjPz",
	"YrkDvmqkUV5g7MjuscLMRyevH1mkKMHpM+/zhFvFzTwnlixJ1gXMX5jqnFCCvfdMYUVUtKesz2GOlzxE",
	"/Ser6kCOHSay7tt05tigyRpeYdp6FTz11DJT5fAmJMbxmODQyWsKjGq3mjX/2fX/BgAA///qvg/K2M8A",
	"AA==",
}

// GetSwagger returns the content of the embedded swagger specification file
// or error if failed to decode
func decodeSpec() ([]byte, error) {
	zipped, err := base64.StdEncoding.DecodeString(strings.Join(swaggerSpec, ""))
	if err != nil {
		return nil, fmt.Errorf("error base64 decoding spec: %w", err)
	}
	zr, err := gzip.NewReader(bytes.NewReader(zipped))
	if err != nil {
		return nil, fmt.Errorf("error decompressing spec: %w", err)
	}
	var buf bytes.Buffer
	_, err = buf.ReadFrom(zr)
	if err != nil {
		return nil, fmt.Errorf("error decompressing spec: %w", err)
	}

	return buf.Bytes(), nil
}

var rawSpec = decodeSpecCached()

// a naive cached of a decoded swagger spec
func decodeSpecCached() func() ([]byte, error) {
	data, err := decodeSpec()
	return func() ([]byte, error) {
		return data, err
	}
}

// Constructs a synthetic filesystem for resolving external references when loading openapi specifications.
func PathToRawSpec(pathToFile string) map[string]func() ([]byte, error) {
	res := make(map[string]func() ([]byte, error))
	if len(pathToFile) > 0 {
		res[pathToFile] = rawSpec
	}

	return res
}

// GetSwagger returns the Swagger specification corresponding to the generated code
// in this file. The external references of Swagger specification are resolved.
// The logic of resolving external references is tightly connected to "import-mapping" feature.
// Externally referenced files must be embedded in the corresponding golang packages.
// Urls can be supported but this task was out of the scope.
func GetSwagger() (swagger *openapi3.T, err error) {
	resolvePath := PathToRawSpec("")

	loader := openapi3.NewLoader()
	loader.IsExternalRefsAllowed = true
	loader.ReadFromURIFunc = func(loader *openapi3.Loader, url *url.URL) ([]byte, error) {
		pathToFile := url.String()
		pathToFile = path.Clean(pathToFile)
		getSpec, ok := resolvePath[pathToFile]
		if !ok {
			err1 := fmt.Errorf("path not found: %s", pathToFile)
			return nil, err1
		}
		return getSpec()
	}
	var specData []byte
	specData, err = rawSpec()
	if err != nil {
		return
	}
	swagger, err = loader.LoadFromData(specData)
	if err != nil {
		return
	}
	return
}
