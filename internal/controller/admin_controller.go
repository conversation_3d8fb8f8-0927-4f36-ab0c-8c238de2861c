package controller

import (
	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"github.com/oapi-codegen/runtime/types"

	api "github.com/smooth-inc/backend/api/generated"
	"github.com/smooth-inc/backend/internal/domain"
	"github.com/smooth-inc/backend/internal/infra/http/response"
	"github.com/smooth-inc/backend/internal/infra/logger"
	"github.com/smooth-inc/backend/internal/usecase"
)

type AdminController struct {
	adminParkingLotUsecase       usecase.AdminParkingLotUsecase
	adminParkingLotConfigUsecase usecase.AdminParkingLotConfigUsecase
	adminUserUsecase             usecase.AdminUserUsecase
	adminPlateUsecase            usecase.AdminPlateUsecase
	adminSessionUsecase          usecase.AdminSessionUsecase
	logger                       *logger.Logger
}

func NewAdminController(
	adminParkingLotUsecase usecase.AdminParkingLotUsecase,
	adminParkingLotConfigUsecase usecase.AdminParkingLotConfigUsecase,
	adminUserUsecase usecase.AdminUserUsecase,
	adminPlateUsecase usecase.AdminPlateUsecase,
	adminSessionUsecase usecase.AdminSessionUsecase,
	logger *logger.Logger,
) *AdminController {
	return &AdminController{
		adminParkingLotUsecase:       adminParkingLotUsecase,
		adminParkingLotConfigUsecase: adminParkingLotConfigUsecase,
		adminUserUsecase:             adminUserUsecase,
		adminPlateUsecase:            adminPlateUsecase,
		adminSessionUsecase:          adminSessionUsecase,
		logger:                       logger,
	}
}

func (ac *AdminController) GetAdminParkingLots(c *gin.Context, params api.GetAdminParkingLotsParams) {
	limit := 20
	if params.Limit != nil {
		limit = *params.Limit
	}
	offset := 0
	if params.Offset != nil {
		offset = *params.Offset
	}

	var status *domain.LotStatus
	if params.Status != nil {
		lotStatus := domain.LotStatus(*params.Status)
		status = &lotStatus
	}

	lots, total, err := ac.adminParkingLotUsecase.List(c.Request.Context(), status, params.Search, limit, offset)
	if err != nil {
		ac.logger.LogError(c.Request.Context(), err, "Failed to list parking lots", nil)
		response.InternalServerError(c, "LIST_PARKING_LOTS_FAILED", "Failed to retrieve parking lots")
		return
	}

	apiLots := make([]api.AdminParkingLotSummary, len(lots))
	for i, lot := range lots {
		lotID := types.UUID(lot.ID)
		lotStatus := api.LotStatus(lot.Status)
		apiLots[i] = api.AdminParkingLotSummary{
			Id:         &lotID,
			Name:       &lot.Name,
			Address:    &lot.Address,
			TotalSpots: &lot.TotalSpots,
			Status:     &lotStatus,
			CreatedAt:  &lot.CreatedAt,
		}
	}

	response.Success(c, api.AdminParkingLotsResponse{
		Lots:   &apiLots,
		Total:  &total,
		Limit:  &limit,
		Offset: &offset,
	})
}

func (ac *AdminController) PostAdminParkingLots(c *gin.Context) {
	var req api.CreateParkingLotRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.BadRequest(c, "INVALID_REQUEST", "Invalid request body", nil)
		return
	}

	hourlyRate := 0
	if req.HourlyRate != nil {
		hourlyRate = *req.HourlyRate
	}

	features := []string{}
	if req.Features != nil {
		features = *req.Features
	}

	lot, err := ac.adminParkingLotUsecase.Create(
		c.Request.Context(),
		req.Name,
		req.Address,
		req.Latitude,
		req.Longitude,
		req.TotalSpots,
		hourlyRate,
		features,
	)
	if err != nil {
		ac.logger.LogError(c.Request.Context(), err, "Failed to create parking lot", nil)
		response.InternalServerError(c, "CREATE_PARKING_LOT_FAILED", "Failed to create parking lot")
		return
	}

	response.Created(c, convertToAPIParkingLot(lot))
}

func (ac *AdminController) GetAdminParkingLotsLotId(c *gin.Context, lotId types.UUID) {
	lot, _, err := ac.adminParkingLotUsecase.GetWithStats(c.Request.Context(), uuid.UUID(lotId))
	if err != nil {
		ac.logger.LogError(c.Request.Context(), err, "Failed to get parking lot", map[string]interface{}{
			"lot_id": lotId,
		})
		response.NotFound(c, "PARKING_LOT_NOT_FOUND", "Parking lot not found")
		return
	}

	lotID := types.UUID(lot.ID)
	lotStatus := api.LotStatus(lot.Status)
	response.Success(c, api.AdminParkingLotDetails{
		Id:            &lotID,
		Name:          &lot.Name,
		Address:       &lot.Address,
		Latitude:      &lot.Latitude,
		Longitude:     &lot.Longitude,
		TotalSpots:    &lot.TotalSpots,
		HourlyRate:    &lot.HourlyRate,
		HeightLimitCm: lot.HeightLimitCm,
		DailyMaxRate:  lot.DailyMaxRate,
		FreeMinutes:   &lot.FreeMinutes,
		Is24h:         &lot.Is24h,
		OpenTime:      lot.OpenTime,
		CloseTime:     lot.CloseTime,
		Features:      &lot.Features,
		Status:        &lotStatus,
		OperatorName:  lot.OperatorName,
		ContactPhone:  lot.ContactPhone,
		CreatedAt:     &lot.CreatedAt,
		UpdatedAt:     &lot.UpdatedAt,
	})
}

func (ac *AdminController) PutAdminParkingLotsLotId(c *gin.Context, lotId types.UUID) {
	var req api.UpdateParkingLotRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.BadRequest(c, "INVALID_REQUEST", "Invalid request body", nil)
		return
	}

	updates := make(map[string]interface{})
	if req.Name != nil {
		updates["name"] = *req.Name
	}
	if req.Address != nil {
		updates["address"] = *req.Address
	}
	if req.Latitude != nil {
		updates["latitude"] = *req.Latitude
	}
	if req.Longitude != nil {
		updates["longitude"] = *req.Longitude
	}
	if req.TotalSpots != nil {
		updates["total_spots"] = *req.TotalSpots
	}
	if req.HourlyRate != nil {
		updates["hourly_rate"] = *req.HourlyRate
	}
	if req.HeightLimitCm != nil {
		updates["height_limit_cm"] = *req.HeightLimitCm
	}
	if req.DailyMaxRate != nil {
		updates["daily_max_rate"] = *req.DailyMaxRate
	}
	if req.FreeMinutes != nil {
		updates["free_minutes"] = *req.FreeMinutes
	}
	if req.Is24h != nil {
		updates["is_24h"] = *req.Is24h
	}
	if req.OpenTime != nil {
		updates["open_time"] = *req.OpenTime
	}
	if req.CloseTime != nil {
		updates["close_time"] = *req.CloseTime
	}
	if req.Features != nil {
		updates["features"] = *req.Features
	}
	if req.Status != nil {
		updates["status"] = string(*req.Status)
	}
	if req.OperatorName != nil {
		updates["operator_name"] = *req.OperatorName
	}
	if req.ContactPhone != nil {
		updates["contact_phone"] = *req.ContactPhone
	}

	lot, err := ac.adminParkingLotUsecase.Update(c.Request.Context(), uuid.UUID(lotId), updates)
	if err != nil {
		ac.logger.LogError(c.Request.Context(), err, "Failed to update parking lot", map[string]interface{}{
			"lot_id": lotId,
		})
		response.InternalServerError(c, "UPDATE_PARKING_LOT_FAILED", "Failed to update parking lot")
		return
	}

	response.Success(c, convertToAPIParkingLot(lot))
}

func (ac *AdminController) DeleteAdminParkingLotsLotId(c *gin.Context, lotId types.UUID) {
	err := ac.adminParkingLotUsecase.Delete(c.Request.Context(), uuid.UUID(lotId))
	if err != nil {
		ac.logger.LogError(c.Request.Context(), err, "Failed to delete parking lot", map[string]interface{}{
			"lot_id": lotId,
		})
		response.InternalServerError(c, "DELETE_PARKING_LOT_FAILED", "Failed to delete parking lot")
		return
	}

	response.NoContent(c)
}

func (ac *AdminController) GetAdminParkingLotsLotIdPricingConfigs(c *gin.Context, lotId types.UUID, params api.GetAdminParkingLotsLotIdPricingConfigsParams) {
	includeInactive := false
	if params.IncludeInactive != nil {
		includeInactive = *params.IncludeInactive
	}

	configs, err := ac.adminParkingLotConfigUsecase.GetByParkingLotID(c.Request.Context(), uuid.UUID(lotId), includeInactive)
	if err != nil {
		ac.logger.LogError(c.Request.Context(), err, "Failed to get pricing configs", map[string]interface{}{
			"lot_id": lotId,
		})
		response.InternalServerError(c, "GET_PRICING_CONFIGS_FAILED", "Failed to retrieve pricing configurations")
		return
	}

	apiConfigs := make([]api.ParkingLotConfig, len(configs))
	for i, config := range configs {
		apiConfigs[i] = convertToAPIParkingLotConfig(config)
	}

	response.Success(c, apiConfigs)
}

func (ac *AdminController) PostAdminParkingLotsLotIdPricingConfigs(c *gin.Context, lotId types.UUID) {
	var req api.CreatePricingConfigRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.BadRequest(c, "INVALID_REQUEST", "Invalid request body", nil)
		return
	}

	userID, exists := c.Get("user_id")
	if !exists {
		response.Unauthorized(c, "UNAUTHORIZED", "User not authenticated")
		return
	}

	createdBy, ok := userID.(uuid.UUID)
	if !ok {
		response.InternalServerError(c, "INVALID_USER_ID", "Invalid user ID format")
		return
	}

	pricingRules := convertFromAPIPricingRules(req.PricingRules)

	config, err := ac.adminParkingLotConfigUsecase.Create(
		c.Request.Context(),
		uuid.UUID(lotId),
		req.ConfigName,
		pricingRules,
		createdBy,
	)
	if err != nil {
		ac.logger.LogError(c.Request.Context(), err, "Failed to create pricing config", map[string]interface{}{
			"lot_id": lotId,
		})
		response.InternalServerError(c, "CREATE_PRICING_CONFIG_FAILED", "Failed to create pricing configuration")
		return
	}

	response.Created(c, convertToAPIParkingLotConfig(config))
}

func (ac *AdminController) GetAdminParkingLotsLotIdPricingConfigsConfigId(c *gin.Context, lotId types.UUID, configId types.UUID) {
	config, err := ac.adminParkingLotConfigUsecase.GetByID(c.Request.Context(), uuid.UUID(configId))
	if err != nil {
		ac.logger.LogError(c.Request.Context(), err, "Failed to get pricing config", map[string]interface{}{
			"lot_id":    lotId,
			"config_id": configId,
		})
		response.NotFound(c, "PRICING_CONFIG_NOT_FOUND", "Pricing configuration not found")
		return
	}

	response.Success(c, convertToAPIParkingLotConfig(config))
}

func (ac *AdminController) PutAdminParkingLotsLotIdPricingConfigsConfigId(c *gin.Context, lotId types.UUID, configId types.UUID) {
	var req api.UpdatePricingConfigRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.BadRequest(c, "INVALID_REQUEST", "Invalid request body", nil)
		return
	}

	var pricingRules *domain.PricingRules
	if req.PricingRules != nil {
		rules := convertFromAPIPricingRules(*req.PricingRules)
		pricingRules = &rules
	}

	config, err := ac.adminParkingLotConfigUsecase.Update(c.Request.Context(), uuid.UUID(configId), req.ConfigName, pricingRules)
	if err != nil {
		ac.logger.LogError(c.Request.Context(), err, "Failed to update pricing config", map[string]interface{}{
			"lot_id":    lotId,
			"config_id": configId,
		})
		response.InternalServerError(c, "UPDATE_PRICING_CONFIG_FAILED", "Failed to update pricing configuration")
		return
	}

	response.Success(c, convertToAPIParkingLotConfig(config))
}

func (ac *AdminController) DeleteAdminParkingLotsLotIdPricingConfigsConfigId(c *gin.Context, lotId types.UUID, configId types.UUID) {
	err := ac.adminParkingLotConfigUsecase.Delete(c.Request.Context(), uuid.UUID(configId))
	if err != nil {
		ac.logger.LogError(c.Request.Context(), err, "Failed to delete pricing config", map[string]interface{}{
			"lot_id":    lotId,
			"config_id": configId,
		})
		response.InternalServerError(c, "DELETE_PRICING_CONFIG_FAILED", "Failed to delete pricing configuration")
		return
	}

	response.NoContent(c)
}

func (ac *AdminController) PostAdminParkingLotsLotIdPricingConfigsConfigIdActivate(c *gin.Context, lotId types.UUID, configId types.UUID) {
	var req api.ActivatePricingConfigRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.BadRequest(c, "INVALID_REQUEST", "Invalid request body", nil)
		return
	}

	config, err := ac.adminParkingLotConfigUsecase.Activate(c.Request.Context(), uuid.UUID(configId), req.EffectiveFrom, req.EffectiveUntil)
	if err != nil {
		ac.logger.LogError(c.Request.Context(), err, "Failed to activate pricing config", map[string]interface{}{
			"lot_id":    lotId,
			"config_id": configId,
		})
		response.InternalServerError(c, "ACTIVATE_PRICING_CONFIG_FAILED", "Failed to activate pricing configuration")
		return
	}

	response.Success(c, convertToAPIParkingLotConfig(config))
}

func (ac *AdminController) PostAdminPricingConfigsValidate(c *gin.Context) {
	var req api.PricingRules
	if err := c.ShouldBindJSON(&req); err != nil {
		response.BadRequest(c, "INVALID_REQUEST", "Invalid request body", nil)
		return
	}

	pricingRules := convertFromAPIPricingRules(req)
	isValid, errors, warnings, err := ac.adminParkingLotConfigUsecase.ValidatePricingRules(c.Request.Context(), pricingRules)
	if err != nil {
		ac.logger.LogError(c.Request.Context(), err, "Failed to validate pricing rules", nil)
		response.InternalServerError(c, "VALIDATION_FAILED", "Failed to validate pricing rules")
		return
	}

	response.Success(c, gin.H{
		"isValid":  isValid,
		"errors":   errors,
		"warnings": warnings,
	})
}

func (ac *AdminController) PostAdminPricingConfigsCalculatePreview(c *gin.Context) {
	var req api.FeeCalculationPreviewRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.BadRequest(c, "INVALID_REQUEST", "Invalid request body", nil)
		return
	}

	pricingRules := convertFromAPIPricingRules(req.PricingRules)
	scenarios := make([]usecase.ParkingScenario, len(req.Scenarios))
	for i, scenario := range req.Scenarios {
		scenarios[i] = usecase.ParkingScenario{
			Name:      scenario.Name,
			EntryTime: scenario.EntryTime,
			ExitTime:  scenario.ExitTime,
		}
	}

	results, err := ac.adminParkingLotConfigUsecase.CalculatePreview(c.Request.Context(), pricingRules, scenarios)
	if err != nil {
		ac.logger.LogError(c.Request.Context(), err, "Failed to calculate preview", nil)
		response.InternalServerError(c, "CALCULATION_FAILED", "Failed to calculate fee preview")
		return
	}

	apiResults := make([]gin.H, len(results))
	for i, result := range results {
		apiResults[i] = gin.H{
			"name":            result.Name,
			"fee":             result.Fee,
			"freeMinutesUsed": result.FreeMinutesUsed,
			"totalMinutes":    result.TotalMinutes,
			"billableMinutes": result.BillableMinutes,
			"breakdown": gin.H{
				"baseFee":         result.Breakdown.BaseFee,
				"nightCapApplied": result.Breakdown.NightCapApplied,
				"dailyCapApplied": result.Breakdown.DailyCapApplied,
				"overrideFee":     result.Breakdown.OverrideFee,
				"discountAmount":  result.Breakdown.DiscountAmount,
				"finalFee":        result.Breakdown.FinalFee,
			},
			"appliedRules": result.AppliedRules,
		}
	}

	response.Success(c, gin.H{
		"results": apiResults,
	})
}

func (ac *AdminController) GetAdminUsers(c *gin.Context, params api.GetAdminUsersParams) {
	limit := 20
	if params.Limit != nil {
		limit = *params.Limit
	}
	offset := 0
	if params.Offset != nil {
		offset = *params.Offset
	}

	search := ""
	if params.Search != nil {
		search = *params.Search
	}

	users, total, err := ac.adminUserUsecase.List(c.Request.Context(), nil, nil, &search, nil, limit, offset)
	if err != nil {
		ac.logger.LogError(c.Request.Context(), err, "Failed to list users", nil)
		response.InternalServerError(c, "LIST_USERS_FAILED", "Failed to retrieve users")
		return
	}

	apiUsers := make([]api.AdminUserSummary, len(users))
	for i, user := range users {
		userID := types.UUID(user.ID)
		userRole := api.UserRole(user.Role)
		apiUsers[i] = api.AdminUserSummary{
			Id:        &userID,
			Email:     &user.Email,
			FullName:  user.Name,
			Status:    (*api.UserStatus)(&user.Status),
			Role:      &userRole,
			CreatedAt: &user.CreatedAt,
		}
	}

	response.Success(c, api.AdminUsersResponse{
		Users:  &apiUsers,
		Total:  &total,
		Limit:  &limit,
		Offset: &offset,
	})
}

func (ac *AdminController) GetAdminUsersUserId(c *gin.Context, userId types.UUID) {
	user, err := ac.adminUserUsecase.GetByID(c.Request.Context(), uuid.UUID(userId))
	if err != nil {
		ac.logger.LogError(c.Request.Context(), err, "Failed to get user", map[string]interface{}{
			"user_id": userId,
		})
		response.NotFound(c, "USER_NOT_FOUND", "User not found")
		return
	}

	// Get related data
	plates, err := ac.adminPlateUsecase.List(c.Request.Context(), &user.ID, nil, nil, nil, 100, 0)
	if err != nil {
		ac.logger.LogError(c.Request.Context(), err, "Failed to get user plates", map[string]interface{}{
			"user_id": userId,
		})
		plates = []*domain.Plate{}
	}

	userID := types.UUID(user.ID)
	userRole := api.UserRole(user.Role)
	apiPlates := make([]api.Plate, len(plates))
	for i, plate := range plates {
		apiPlates[i] = convertToAPIPlate(plate)
	}

	// Payment methods will be empty for now
	apiPaymentMethods := []api.PaymentMethod{}

	// Sessions will be empty for now
	apiSessionSummaries := []api.SessionSummary{}

	response.Success(c, api.AdminUserDetails{
		Id:             &userID,
		Email:          &user.Email,
		FullName:       user.Name,
		Status:         (*api.UserStatus)(&user.Status),
		Role:           &userRole,
		CreatedAt:      &user.CreatedAt,
		UpdatedAt:      &user.UpdatedAt,
		Plates:         &apiPlates,
		PaymentMethods: &apiPaymentMethods,
		ActiveSessions: &apiSessionSummaries,
	})
}

func (ac *AdminController) GetAdminPlates(c *gin.Context, params api.GetAdminPlatesParams) {
	limit := 20
	if params.Limit != nil {
		limit = *params.Limit
	}
	offset := 0
	if params.Offset != nil {
		offset = *params.Offset
	}

	var userID *uuid.UUID
	if params.UserId != nil {
		uid := uuid.UUID(*params.UserId)
		userID = &uid
	}

	plates, total, err := ac.adminPlateUsecase.List(c.Request.Context(), userID, nil, nil, nil, limit, offset)
	if err != nil {
		ac.logger.LogError(c.Request.Context(), err, "Failed to list plates", nil)
		response.InternalServerError(c, "LIST_PLATES_FAILED", "Failed to retrieve plates")
		return
	}

	apiPlates := make([]api.AdminPlateSummary, len(plates))
	for i, plate := range plates {
		plateID := types.UUID(plate.ID)
		userID := types.UUID(plate.UserID)

		// Get user info for each plate
		user, err := ac.adminUserUsecase.GetByID(c.Request.Context(), plate.UserID)
		if err != nil {
			ac.logger.LogError(c.Request.Context(), err, "Failed to get user for plate", map[string]interface{}{
				"plate_id": plate.ID,
				"user_id":  plate.UserID,
			})
			continue
		}

		apiPlates[i] = api.AdminPlateSummary{
			Id:          &plateID,
			PlateNumber: &plate.PlateNumber,
			IsActive:    &plate.IsActive,
			IsInSession: &plate.IsInSession,
			User: &api.AdminUserSummary{
				Id:       &userID,
				Email:    &user.Email,
				FullName: user.Name,
			},
			CreatedAt: &plate.CreatedAt,
		}
	}

	response.Success(c, api.AdminPlatesResponse{
		Plates: &apiPlates,
		Total:  &total,
		Limit:  &limit,
		Offset: &offset,
	})
}

func (ac *AdminController) GetAdminPlatesPlateId(c *gin.Context, plateId types.UUID) {
	plate, err := ac.adminPlateUsecase.GetByID(c.Request.Context(), uuid.UUID(plateId))
	if err != nil {
		ac.logger.LogError(c.Request.Context(), err, "Failed to get plate", map[string]interface{}{
			"plate_id": plateId,
		})
		response.NotFound(c, "PLATE_NOT_FOUND", "Plate not found")
		return
	}

	// Get user info
	user, err := ac.adminUserUsecase.GetByID(c.Request.Context(), plate.UserID)
	if err != nil {
		ac.logger.LogError(c.Request.Context(), err, "Failed to get user for plate", map[string]interface{}{
			"plate_id": plateId,
			"user_id":  plate.UserID,
		})
		response.InternalServerError(c, "GET_USER_FAILED", "Failed to get user information")
		return
	}

	plateID := types.UUID(plate.ID)
	userID := types.UUID(plate.UserID)

	apiSessionSummaries := []api.SessionSummary{}

	response.Success(c, api.AdminPlateDetails{
		Id:          &plateID,
		PlateNumber: &plate.PlateNumber,
		IsActive:    &plate.IsActive,
		IsInSession: &plate.IsInSession,
		User: &api.AdminUserSummary{
			Id:       &userID,
			Email:    &user.Email,
			FullName: user.Name,
		},
		Sessions:  &apiSessionSummaries,
		CreatedAt: &plate.CreatedAt,
		UpdatedAt: &plate.UpdatedAt,
	})
}

func (ac *AdminController) GetAdminSessions(c *gin.Context, params api.GetAdminSessionsParams) {
	limit := 20
	if params.Limit != nil {
		limit = *params.Limit
	}
	offset := 0
	if params.Offset != nil {
		offset = *params.Offset
	}

	filters := make(map[string]interface{})
	if params.Status != nil {
		filters["status"] = string(*params.Status)
	}
	if params.ParkingLotId != nil {
		filters["parking_lot_id"] = *params.ParkingLotId
	}
	if params.UserId != nil {
		filters["user_id"] = *params.UserId
	}
	if params.DateFrom != nil {
		filters["date_from"] = *params.DateFrom
	}
	if params.DateTo != nil {
		filters["date_to"] = *params.DateTo
	}

	sessions, total, err := ac.adminSessionUsecase.List(c.Request.Context(), filters, limit, offset)
	if err != nil {
		ac.logger.LogError(c.Request.Context(), err, "Failed to list sessions", nil)
		response.InternalServerError(c, "LIST_SESSIONS_FAILED", "Failed to retrieve sessions")
		return
	}

	apiSessions := make([]api.AdminSessionSummary, len(sessions))
	for i, session := range sessions {
		sessionID := types.UUID(session.ID)
		parkingLotID := types.UUID(session.ParkingLotID)
		userID := types.UUID(session.UserID)
		plateID := types.UUID(session.PlateID)
		status := api.SessionStatus(session.Status)

		// Get parking lot info
		lot, err := ac.adminParkingLotUsecase.GetByID(c.Request.Context(), session.ParkingLotID)
		if err != nil {
			ac.logger.LogError(c.Request.Context(), err, "Failed to get parking lot", map[string]interface{}{
				"session_id": session.ID,
				"lot_id":     session.ParkingLotID,
			})
			continue
		}

		// Get user info
		user, err := ac.adminUserUsecase.GetByID(c.Request.Context(), session.UserID)
		if err != nil {
			ac.logger.LogError(c.Request.Context(), err, "Failed to get user", map[string]interface{}{
				"session_id": session.ID,
				"user_id":    session.UserID,
			})
			continue
		}

		// Get plate info
		plate, err := ac.adminPlateUsecase.GetByID(c.Request.Context(), session.PlateID)
		if err != nil {
			ac.logger.LogError(c.Request.Context(), err, "Failed to get plate", map[string]interface{}{
				"session_id": session.ID,
				"plate_id":   session.PlateID,
			})
			continue
		}

		apiSessions[i] = api.AdminSessionSummary{
			Id: &sessionID,
			ParkingLot: &api.AdminParkingLotSummary{
				Id:   &parkingLotID,
				Name: &lot.Name,
			},
			User: &api.AdminUserSummary{
				Id:    &userID,
				Email: &user.Email,
			},
			Plate: &api.AdminPlateSummary{
				Id:          &plateID,
				PlateNumber: &plate.PlateNumber,
			},
			CheckInTime:  &session.CheckInTime,
			CheckOutTime: session.CheckOutTime,
			TotalAmount:  session.TotalAmount,
			Status:       &status,
		}
	}

	response.Success(c, api.AdminSessionsResponse{
		Sessions: &apiSessions,
		Total:    &total,
		Limit:    &limit,
		Offset:   &offset,
	})
}

func (ac *AdminController) GetAdminSessionsSessionId(c *gin.Context, sessionId types.UUID) {
	session, err := ac.adminSessionUsecase.GetByID(c.Request.Context(), uuid.UUID(sessionId))
	if err != nil {
		ac.logger.LogError(c.Request.Context(), err, "Failed to get session details", map[string]interface{}{
			"session_id": sessionId,
		})
		response.NotFound(c, "SESSION_NOT_FOUND", "Session not found")
		return
	}

	sessionID := types.UUID(session.ID)
	parkingLotID := types.UUID(session.ParkingLotID)
	userID := types.UUID(session.UserID)
	plateID := types.UUID(session.PlateID)
	status := api.SessionStatus(session.Status)

	var paymentID *types.UUID
	var paymentMethod *string
	var paymentStatus *api.PaymentStatus
	if session.Payment != nil {
		pid := types.UUID(session.Payment.ID)
		paymentID = &pid
		paymentMethod = &session.Payment.PaymentMethod
		ps := api.PaymentStatus(session.Payment.Status)
		paymentStatus = &ps
	}

	response.Success(c, api.AdminSessionDetails{
		Id: &sessionID,
		ParkingLot: &api.AdminParkingLotSummary{
			Id:      &parkingLotID,
			Name:    &session.ParkingLot.Name,
			Address: &session.ParkingLot.Address,
		},
		User: &api.AdminUserSummary{
			Id:       &userID,
			Email:    &session.User.Email,
			FullName: session.User.FullName,
		},
		Plate: &api.AdminPlateSummary{
			Id:          &plateID,
			PlateNumber: &session.Plate.PlateNumber,
		},
		CheckInTime:  &session.CheckInTime,
		CheckOutTime: session.CheckOutTime,
		TotalAmount:  session.TotalAmount,
		Status:       &status,
		Payment: &api.AdminPaymentSummary{
			Id:            paymentID,
			Amount:        session.TotalAmount,
			PaymentMethod: paymentMethod,
			Status:        paymentStatus,
		},
		CreatedAt: &session.CreatedAt,
		UpdatedAt: &session.UpdatedAt,
	})
}

// Helper functions
func convertToAPIParkingLot(lot *domain.ParkingLot) api.ParkingLot {
	return api.ParkingLot{
		Id:             lot.ID,
		Name:           lot.Name,
		Address:        lot.Address,
		Latitude:       lot.Latitude,
		Longitude:      lot.Longitude,
		TotalSpots:     lot.TotalSpots,
		AvailableSpots: lot.AvailableSpots,
		HourlyRate:     lot.HourlyRate,
		HeightLimitCm:  lot.HeightLimitCm,
		DailyMaxRate:   lot.DailyMaxRate,
		FreeMinutes:    lot.FreeMinutes,
		Is24h:          lot.Is24h,
		OpenTime:       lot.OpenTime,
		CloseTime:      lot.CloseTime,
		Features:       lot.Features,
		Status:         api.LotStatus(lot.Status),
		OperatorName:   lot.OperatorName,
		ContactPhone:   lot.ContactPhone,
		CreatedAt:      lot.CreatedAt,
		UpdatedAt:      lot.UpdatedAt,
	}
}

func convertToAPIParkingLotConfig(config *domain.ParkingLotConfig) api.ParkingLotConfig {
	return api.ParkingLotConfig{
		Id:             config.ID,
		ParkingLotId:   config.ParkingLotID,
		ConfigName:     config.ConfigName,
		PricingRules:   convertToAPIPricingRules(config.PricingRules),
		IsActive:       config.IsActive,
		EffectiveFrom:  config.EffectiveFrom,
		EffectiveUntil: config.EffectiveUntil,
		CreatedBy:      config.CreatedBy,
		CreatedAt:      config.CreatedAt,
		UpdatedAt:      config.UpdatedAt,
	}
}

func convertToAPIPricingRules(rules domain.PricingRules) api.PricingRules {
	apiRules := make([]api.PricingRule, len(rules.Rules))
	for i, rule := range rules.Rules {
		apiRules[i] = api.PricingRule{
			Days:         rule.Days,
			Start:        rule.Start,
			End:          rule.End,
			UnitMinutes:  rule.UnitMinutes,
			PricePerUnit: rule.PricePerUnit,
		}
	}

	var apiOverrides []api.PricingOverride
	if rules.Overrides != nil {
		apiOverrides = make([]api.PricingOverride, len(*rules.Overrides))
		for i, override := range *rules.Overrides {
			apiOverrides[i] = api.PricingOverride{
				Name:         override.Name,
				Start:        override.Start,
				End:          override.End,
				UnitMinutes:  override.UnitMinutes,
				PricePerUnit: override.PricePerUnit,
			}
		}
	}

	var apiNightCaps []api.NightCap
	if rules.NightCaps != nil {
		apiNightCaps = make([]api.NightCap, len(*rules.NightCaps))
		for i, cap := range *rules.NightCaps {
			apiNightCaps[i] = api.NightCap{
				Start: cap.Start,
				End:   cap.End,
				Cap:   cap.Cap,
			}
		}
	}

	return api.PricingRules{
		InitialFreeMinutes: rules.InitialFreeMinutes,
		DailyCap:           rules.DailyCap,
		Rules:              apiRules,
		Overrides:          &apiOverrides,
		NightCaps:          &apiNightCaps,
	}
}

func convertFromAPIPricingRules(apiRules api.PricingRules) domain.PricingRules {
	rules := make([]domain.PricingRule, len(apiRules.Rules))
	for i, rule := range apiRules.Rules {
		rules[i] = domain.PricingRule{
			Days:         rule.Days,
			Start:        rule.Start,
			End:          rule.End,
			UnitMinutes:  rule.UnitMinutes,
			PricePerUnit: rule.PricePerUnit,
		}
	}

	var overrides *[]domain.PricingOverride
	if apiRules.Overrides != nil {
		domainOverrides := make([]domain.PricingOverride, len(*apiRules.Overrides))
		for i, override := range *apiRules.Overrides {
			domainOverrides[i] = domain.PricingOverride{
				Name:         override.Name,
				Start:        override.Start,
				End:          override.End,
				UnitMinutes:  override.UnitMinutes,
				PricePerUnit: override.PricePerUnit,
			}
		}
		overrides = &domainOverrides
	}

	var nightCaps *[]domain.NightCap
	if apiRules.NightCaps != nil {
		domainNightCaps := make([]domain.NightCap, len(*apiRules.NightCaps))
		for i, cap := range *apiRules.NightCaps {
			domainNightCaps[i] = domain.NightCap{
				Start: cap.Start,
				End:   cap.End,
				Cap:   cap.Cap,
			}
		}
		nightCaps = &domainNightCaps
	}

	return domain.PricingRules{
		InitialFreeMinutes: apiRules.InitialFreeMinutes,
		DailyCap:           apiRules.DailyCap,
		Rules:              rules,
		Overrides:          overrides,
		NightCaps:          nightCaps,
	}
}

func convertToAPIPlate(plate *domain.Plate) api.Plate {
	return api.Plate{
		Id:          plate.ID,
		UserId:      plate.UserID,
		PlateNumber: plate.PlateNumber,
		IsActive:    plate.IsActive,
		IsInSession: plate.IsInSession,
		CreatedAt:   plate.CreatedAt,
		UpdatedAt:   plate.UpdatedAt,
	}
}

func convertToAPIPaymentMethod(pm *domain.PaymentMethod) api.PaymentMethod {
	return api.PaymentMethod{
		Id:                    pm.ID,
		UserId:                pm.UserID,
		Type:                  pm.Type,
		Last4:                 pm.Last4,
		Brand:                 pm.Brand,
		ExpiryMonth:           pm.ExpiryMonth,
		ExpiryYear:            pm.ExpiryYear,
		IsDefault:             pm.IsDefault,
		StripePaymentMethodId: pm.StripePaymentMethodID,
		CreatedAt:             pm.CreatedAt,
		UpdatedAt:             pm.UpdatedAt,
	}
}

func convertToAPISessionSummary(session *domain.Session) api.SessionSummary {
	return api.SessionSummary{
		Id:           session.ID,
		ParkingLotId: session.ParkingLotID,
		UserId:       session.UserID,
		PlateId:      session.PlateID,
		CheckInTime:  session.CheckInTime,
		CheckOutTime: session.CheckOutTime,
		TotalAmount:  session.TotalAmount,
		Status:       api.SessionStatus(session.Status),
		CreatedAt:    session.CreatedAt,
	}
}
