package server

import (
	"context"
	"fmt"
	"net/http"
	"time"

	"github.com/gin-gonic/gin"

	api "github.com/smooth-inc/backend/api/generated"
	"github.com/smooth-inc/backend/internal/controller"
	"github.com/smooth-inc/backend/internal/infra/http/middleware"
	"github.com/smooth-inc/backend/internal/infra/logger"
	"github.com/smooth-inc/backend/internal/usecase"
)

type Config struct {
	Port         int
	ReadTimeout  time.Duration
	WriteTimeout time.Duration
	IdleTimeout  time.Duration
	Environment  string
}

type Server struct {
	config           Config
	controller       *controller.Controller
	logger           *logger.Logger
	jwtService       usecase.JWTService
	httpServer       *http.Server
	rateLimiter      *middleware.RateLimiter
	metricsCollector *middleware.MetricsCollector
}

func New(config Config, controller *controller.Controller, logger *logger.Logger, jwtService usecase.JWTService) *Server {
	s := &Server{
		config:     config,
		controller: controller,
		logger:     logger,
		jwtService: jwtService,
	}

	s.setupComponents()
	s.setupServer()
	return s
}

func (s *Server) setupComponents() {
	var rateLimiterConfig middleware.RateLimiterConfig
	if s.config.Environment == "production" {
		rateLimiterConfig = middleware.StrictRateLimiterConfig()
	} else {
		rateLimiterConfig = middleware.DefaultRateLimiterConfig()
	}
	s.rateLimiter = middleware.NewRateLimiter(rateLimiterConfig)

	var metricsConfig middleware.MetricsConfig
	if s.config.Environment == "production" {
		metricsConfig = middleware.DefaultMetricsConfig()
	} else {
		metricsConfig = middleware.DetailedMetricsConfig()
	}
	s.metricsCollector = middleware.NewMetricsCollector(metricsConfig, s.logger)
}

func (s *Server) setupServer() {
	router := gin.New()

	s.setupMiddleware(router)
	s.setupRoutes(router)

	s.httpServer = &http.Server{
		Addr:         s.getAddress(),
		Handler:      router,
		ReadTimeout:  s.config.ReadTimeout,
		WriteTimeout: s.config.WriteTimeout,
		IdleTimeout:  s.config.IdleTimeout,
	}
}

func (s *Server) setupMiddleware(router *gin.Engine) {
	router.Use(middleware.RecoveryMiddleware(s.logger))

	router.Use(middleware.MetricsMiddleware(s.metricsCollector))

	var rateLimiterConfig middleware.RateLimiterConfig
	if s.config.Environment == "production" {
		rateLimiterConfig = middleware.StrictRateLimiterConfig()
	} else {
		rateLimiterConfig = middleware.DefaultRateLimiterConfig()
	}
	router.Use(middleware.RateLimitMiddleware(rateLimiterConfig))

	var validationConfig middleware.ValidationConfig
	if s.config.Environment == "production" {
		validationConfig = middleware.StrictValidationConfig()
	} else {
		validationConfig = middleware.DefaultValidationConfig()
	}
	router.Use(s.conditionalValidationMiddleware(validationConfig))

	router.Use(middleware.RequestLoggingMiddleware(s.logger))

	corsConfig := middleware.DefaultCORSConfig()
	if s.config.Environment == "production" {
		corsConfig.AllowOrigins = []string{
			"https://smooth.inc",
			"https://app.smooth.inc",
			"https://admin.smooth.inc",
		}
	}
	router.Use(middleware.CORSMiddleware(corsConfig))
	router.Use(middleware.TimeoutMiddleware(s.logger))

	router.GET("/metrics", s.metricsEndpoint)
}

func (s *Server) setupRoutes(router *gin.Engine) {
	v1 := router.Group("/api/v1")

	v1.GET("/health", s.healthCheck)
	v1.GET("/status", func(c *gin.Context) {
		c.JSON(http.StatusOK, gin.H{
			"status":  "ok",
			"service": "smooth-backend",
			"version": "1.0.0",
		})
	})

	authMiddleware := middleware.JWTAuthMiddleware(s.jwtService, s.logger)

	var apiKeyConfig middleware.APIKeyConfig
	if s.config.Environment == "production" {
		apiKeyConfig = middleware.SecureAPIKeyConfig()
	} else {
		apiKeyConfig = middleware.DefaultAPIKeyConfig()
	}
	apiKeyMiddleware := middleware.APIKeyAuthMiddleware(apiKeyConfig, s.logger)

	adminMiddleware := middleware.AdminAuthMiddleware(s.logger)

	middlewares := s.createMiddlewareMap(authMiddleware, apiKeyMiddleware, adminMiddleware)

	options := api.GinServerOptions{
		BaseURL:      "/api/v1",
		Middlewares:  middlewares,
		ErrorHandler: s.errorHandler,
	}

	api.RegisterHandlersWithOptions(router, s.controller, options)

	s.logger.LogInfo(context.Background(), "Routes configured successfully using generated API registration")
}

func (s *Server) createMiddlewareMap(authMiddleware, apiKeyMiddleware, adminMiddleware gin.HandlerFunc) []api.MiddlewareFunc {
	return []api.MiddlewareFunc{
		func(c *gin.Context) {
			path := c.Request.URL.Path
			method := c.Request.Method

			switch {
			case s.requiresAdminAuth(path, method):
				authMiddleware(c)
				if !c.IsAborted() {
					adminMiddleware(c)
				}
			case s.requiresAPIKey(path, method):
				apiKeyMiddleware(c)
			case s.isPublicEndpoint(path, method):
				// No authentication required
				return
	case s.isWebhookEndpoint(path, method):
		// Webhook endpoints need special handling
		return
			default:
				// All other endpoints require authentication
				authMiddleware(c)
			}
		},
	}
}

func (s *Server) isPublicEndpoint(path, method string) bool {
	publicPaths := map[string][]string{
		"/api/v1/health":                   {"GET"},
		"/api/v1/status":                   {"GET"},
		"/api/v1/auth/register":            {"POST"},
		"/api/v1/auth/login":               {"POST"},
		"/api/v1/auth/refresh":             {"POST"},
		"/api/v1/auth/forgot-password":     {"POST"},
		"/api/v1/auth/reset-password":      {"POST"},
		"/api/v1/auth/verify-email":        {"GET"},
		"/api/v1/auth/resend-verification": {"POST"},
		"/api/v1/parking-lots":             {"GET"},
		"/api/v1/payments/webhooks/stripe": {"POST"},
		"/metrics":                         {"GET"},
	}

	if methods, exists := publicPaths[path]; exists {
		for _, m := range methods {
			if m == method {
				return true
			}
		}
	}

	// Check for path patterns that are public
	if s.isPathPattern(path, "/api/v1/parking-lots/", method, []string{"GET"}) {
		return true
	}

	return false
}

func (s *Server) isWebhookEndpoint(path, method string) bool {
	// Stripe webhooks should be treated as public endpoints but with special validation
	if path == "/api/v1/payments/webhooks/stripe" && method == "POST" {
		return true
	}
	return false
}

func (s *Server) conditionalValidationMiddleware(config middleware.ValidationConfig) gin.HandlerFunc {
	return func(c *gin.Context) {
		path := c.Request.URL.Path
		method := c.Request.Method

		// Skip validation for webhook endpoints
		if s.isWebhookEndpoint(path, method) {
			c.Next()
			return
		}

		// Apply normal validation for all other endpoints
		middleware.RequestValidationMiddleware(config, s.logger)(c)
	}
}

func (s *Server) requiresAdminAuth(path, method string) bool {
	return s.isPathPattern(path, "/api/v1/admin/", method, []string{"GET", "POST", "PUT", "DELETE", "PATCH"})
}

func (s *Server) requiresAPIKey(path, method string) bool {
	return s.isPathPattern(path, "/api/v1/hardware/", method, []string{"POST"})
}

func (s *Server) isPathPattern(path, pattern, method string, allowedMethods []string) bool {
	if len(path) < len(pattern) {
		return false
	}

	if path[:len(pattern)] != pattern {
		return false
	}

	for _, m := range allowedMethods {
		if m == method {
			return true
		}
	}

	return false
}

func (s *Server) errorHandler(c *gin.Context, err error, statusCode int) {
	s.logger.LogError(c.Request.Context(), err, "API error", map[string]interface{}{
		"status_code": statusCode,
		"path":        c.Request.URL.Path,
		"method":      c.Request.Method,
	})

	c.JSON(statusCode, gin.H{
		"error":   "API_ERROR",
		"message": err.Error(),
	})
}

func (s *Server) healthCheck(c *gin.Context) {
	c.JSON(http.StatusOK, gin.H{
		"status":    "healthy",
		"timestamp": time.Now().UTC(),
		"service":   "smooth-backend",
		"version":   "1.0.0",
	})
}

func (s *Server) metricsEndpoint(c *gin.Context) {
	if s.metricsCollector != nil {
		metrics := s.metricsCollector.GetMetrics()
		c.JSON(http.StatusOK, metrics)
	} else {
		c.JSON(http.StatusServiceUnavailable, gin.H{"error": "Metrics not available"})
	}
}

func (s *Server) getAddress() string {
	if s.config.Port == 0 {
		s.config.Port = 8080
	}
	return fmt.Sprintf(":%d", s.config.Port)
}

func (s *Server) GetHTTPServer() *http.Server {
	return s.httpServer
}

func (s *Server) GetRateLimiter() *middleware.RateLimiter {
	return s.rateLimiter
}

func (s *Server) GetMetricsCollector() *middleware.MetricsCollector {
	return s.metricsCollector
}
